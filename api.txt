# Azure OpenAI API Configuration
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# Build Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"




# GPT-4o Azure OpenAI API 配置
AZURE_OPENAI_API_KEY = os.getenv('AZURE_OPENAI_API_KEY_4O', '1ZVPYTWJTBRce59D4W6hYCeTiIsBfwSgQzaqFCQqtUV3ldrCZRIvJQQJ99BEACYeBjFXJ3w3AAABACOG7uxm')
ENDPOINT_URL = os.getenv('ENDPOINT_URL_4O', 'https://nerllm-4o.openai.azure.com/')
DEPLOYMENT_NAME = os.getenv('DEPLOYMENT_NAME_4O', 'gpt-4o')
API_VERSION = os.getenv('API_VERSION_4O', '2025-01-01-preview')

# 配置API密钥
API_KEY = os.getenv("AZURE_OPENAI_API_KEY_4O", AZURE_OPENAI_API_KEY)
# 从环境变量读取模拟模式设置，默认不使用模拟模式
USE_MOCK_API = os.getenv("USE_MOCK_API", "false").lower() == "true"

# Initialize Azure OpenAI client
client = None
if API_KEY and not USE_MOCK_API:
    try:
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=ENDPOINT_URL
        )
        print("Azure OpenAI client initialized successfully")
    except Exception as e:
        print(f"Error initializing Azure OpenAI client: {e}")
        print("Falling back to mock mode")
        USE_MOCK_API = True




    # 从模型参数获取 API 配置，如果没有则使用默认值
    api_key = model_para.get("api_key", "c598dbbb-4d28-4daf-8898-ee2d0aad8cfe")
    api_base = model_para.get("api_base", "https://ark.cn-beijing.volces.com/api/v3")
    model_name = model_para.get("model", "deepseek-r1-250528")
    
    print(f"DEBUG: 使用ChatOpenAI连接 - API Base: {api_base}")
    print(f"DEBUG: 使用模型: {model_name}")
    
    try:
        # 使用 ChatOpenAI 替代 Ollama
        llm = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base=api_base,
            streaming=True,
            callbacks=[streaming_callback]
        )