#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU集成模块
用于将MinerU的PDF解析功能直接集成到文件上传处理流程中
"""

import os
import json
import tempfile
import logging
import base64
import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from openai import OpenAI

try:
    from magic_pdf.api.magic_pdf_parse import magic_pdf_parse
    from magic_pdf.config.make_content_config import DropMode, MakeContentConfig
    MINERU_AVAILABLE = True
    print("✅ [INTEGRATION_DEBUG] 直接MinerU导入成功")
except ImportError as e:
    print(f"⚠️  [INTEGRATION_DEBUG] 直接MinerU导入失败: {str(e)}")
    print("🔍 [INTEGRATION_DEBUG] 检查虚拟环境版本...")

    # 检查虚拟环境版本是否可用
    try:
        import os
        venv_path = os.path.join(os.getcwd(), "mineru_venv")
        if os.path.exists(venv_path):
            python_path = os.path.join(venv_path, 'bin', 'python') if os.name != 'nt' else os.path.join(venv_path, 'Scripts', 'python.exe')
            if os.path.exists(python_path):
                print("✅ [INTEGRATION_DEBUG] 发现虚拟环境，设置为可用")
                MINERU_AVAILABLE = True  # 虚拟环境可用时设置为True
            else:
                print("❌ [INTEGRATION_DEBUG] 虚拟环境Python不存在")
                MINERU_AVAILABLE = False
        else:
            print("❌ [INTEGRATION_DEBUG] 虚拟环境不存在")
            MINERU_AVAILABLE = False
    except Exception as venv_e:
        print(f"❌ [INTEGRATION_DEBUG] 检查虚拟环境失败: {str(venv_e)}")
        MINERU_AVAILABLE = False

    if not MINERU_AVAILABLE:
        logging.warning("MinerU not available. Please install with: pip install magic-pdf[full]")

    print(f"🎯 [INTEGRATION_DEBUG] 最终设置 MINERU_AVAILABLE = {MINERU_AVAILABLE}")

try:
    from mineru_config import get_mineru_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    logging.warning("MinerU config not available. Using default settings.")

class MinerUProcessor:
    """MinerU处理器，用于PDF文档的布局分析和内容提取"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化MinerU处理器

        Args:
            config_path: MinerU配置文件路径，如果为None则使用默认配置
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)

        if not MINERU_AVAILABLE:
            raise ImportError("MinerU not available. Please install with: pip install magic-pdf[full]")

        # 加载配置
        if CONFIG_AVAILABLE:
            self.config_manager = get_mineru_config()
            if config_path:
                self.config_manager.load_from_file(config_path)
        else:
            self.config_manager = None

        # 初始化InternVL客户端用于图像OCR处理
        self.client = OpenAI(api_key='ollama', base_url="http://*************:30291/v1")

    def png_image_to_base64_data_uri(self, file_path):
        """将PNG图像转换为base64数据URI"""
        with open(file_path, "rb") as img_file:
            base64_data = base64.b64encode(img_file.read()).decode('utf-8')
            return f"data:image/png;base64,{base64_data}"

    def extract_actual_text_content(self, ocr_text):
        """
        Extract actual text content from OCR result, filtering out pure descriptions
        """
        if not ocr_text or not ocr_text.strip():
            return ""

        # Patterns to identify actual text content (not descriptions)
        text_content_patterns = [
            r'[A-Z]{2,}',  # Uppercase words (likely titles, names)
            r'\d+',        # Numbers
            r'[\u4e00-\u9fff]+',  # Chinese characters
            r'[A-Za-z]+\s+[A-Za-z]+',  # Multi-word phrases
            r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # Title case phrases
        ]

        # First check for explicit "NO TEXT" responses
        if re.search(r'\bNO\s+TEXT\b', ocr_text, re.IGNORECASE):
            return ""

        # Extract potential text content
        actual_text_parts = []

        # Look for quoted text or text after colons (often actual content)
        quoted_text = re.findall(r'["\']([^"\']+)["\']', ocr_text)
        colon_text = re.findall(r':\s*[-\s]*([A-Za-z\u4e00-\u9fff][^.!?]*)', ocr_text)

        actual_text_parts.extend(quoted_text)
        actual_text_parts.extend(colon_text)

        # Look for patterns that indicate actual text content
        for pattern in text_content_patterns:
            matches = re.findall(pattern, ocr_text)
            actual_text_parts.extend(matches)

        # Filter out common descriptive words
        descriptive_words = {
            'image', 'shows', 'contains', 'features', 'displays', 'diagram', 'circle',
            'line', 'center', 'top', 'bottom', 'left', 'right', 'section', 'divided',
            'background', 'color', 'pattern', 'design', 'geometric', 'shapes', 'lines',
            'contrast', 'visible', 'present', 'letter', 'simple', 'through', 'intersect',
            'text', 'no', 'content', 'triangles', 'arranged', 'repetitive', 'tessellated',
            'solid', 'teal', 'series'
        }

        # Clean and filter actual text
        meaningful_text = []
        for text in actual_text_parts:
            text = text.strip()
            if len(text) > 1 and text.lower() not in descriptive_words:
                # Check if it's not just a single descriptive word
                words = text.split()
                # Filter out words that are all descriptive
                meaningful_words = [w for w in words if w.lower() not in descriptive_words]
                if len(meaningful_words) > 0:
                    meaningful_text.append(' '.join(meaningful_words))

        # Remove duplicates while preserving order
        seen = set()
        unique_text = []
        for text in meaningful_text:
            if text.strip() and text.lower() not in seen:
                seen.add(text.lower())
                unique_text.append(text)

        result = ' '.join(unique_text)
        return result.strip()

    def clean_ocr_text_with_ai(self, ocr_text):
        """
        Use AI to clean OCR text and extract only actual text content
        """
        if not ocr_text or not ocr_text.strip():
            return ""

        try:
            # Use AI to filter and extract actual text content
            filter_messages = [
                {
                    "role": "user",
                    "content": f"""Please extract ONLY the actual text content from the following OCR result. Remove all descriptive language, formatting instructions, and image descriptions. Return only the readable text that was actually visible in the image.

If there is no actual readable text content, respond with "NO_CONTENT".

OCR Result:
{ocr_text}

Instructions:
- Remove phrases like "The image contains", "Here is the text", "Certainly", etc.
- Remove markdown formatting (###, **, etc.)
- Remove descriptions of image layout or design
- Keep only actual text, numbers, names, addresses, etc. that were visible in the image
- Preserve the original language (Chinese, English, etc.)
- If it's just describing patterns, shapes, or designs with no actual text, return "NO_CONTENT"

Extracted text content:"""
                }
            ]

            response = self.client.chat.completions.create(
                model="OpenGVLab/InternVL3-78B-AWQ",
                messages=filter_messages,
                stream=False,
                max_tokens=500,
                temperature=0.1
            )

            cleaned_text = response.choices[0].message.content.strip()

            # Check if AI determined there's no content
            if cleaned_text.upper() == "NO_CONTENT" or not cleaned_text:
                return ""

            # Basic cleanup of any remaining artifacts
            cleaned_text = re.sub(r'^[:\-\s]+', '', cleaned_text)
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
            cleaned_text = cleaned_text.strip()

            print(f"🤖 AI cleaned OCR: '{ocr_text[:100]}...' -> '{cleaned_text[:100]}...'")
            return cleaned_text

        except Exception as e:
            print(f"❌ AI cleaning failed: {str(e)}, falling back to original")
            # Fallback to original text if AI cleaning fails
            return ocr_text.strip()

    def clean_ocr_text(self, ocr_text):
        """
        Clean OCR text to remove descriptive prefixes and improve search relevance
        Uses AI-based cleaning for better accuracy
        """
        # Try AI-based cleaning first
        ai_cleaned = self.clean_ocr_text_with_ai(ocr_text)

        # If AI cleaning worked and produced meaningful content, use it
        if ai_cleaned and len(ai_cleaned) >= 5:
            return ai_cleaned

        # Fallback to regex-based cleaning if AI didn't work or produced no content
        if not ocr_text or not ocr_text.strip():
            return ""

        # Simple fallback cleaning
        result = ocr_text.strip()

        # Remove common prefixes
        prefixes_to_remove = [
            r'^The image contains?.*?:?\s*',
            r'^Certainly[!.]?\s*',
            r'^Here is.*?:?\s*',
            r'the following text:\s*',
        ]

        for pattern in prefixes_to_remove:
            result = re.sub(pattern, '', result, flags=re.IGNORECASE)

        # Basic cleanup
        result = re.sub(r'^[:\-\s]+', '', result)
        result = re.sub(r'\s+', ' ', result)
        result = result.strip()

        return result

    def process_image_with_internvl(self, image_path, min_text_length=10):
        """
        使用InternVL处理图像并执行OCR文字识别

        Args:
            image_path: 图像文件路径
            min_text_length: 最小文字长度阈值

        Returns:
            tuple: (ocr_text, should_include) - OCR提取的文字和是否应该包含此图像
        """
        try:
            if not os.path.exists(image_path):
                print(f"WARNING: 图像文件不存在: {image_path}")
                return "", False

            try:
                # 使用InternVL进行OCR处理
                image_base64 = self.png_image_to_base64_data_uri(image_path)

                # Step 1: Quick text extraction check
                quick_messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Extract only the readable text content from this image. List any visible text, numbers, or words. If there is no readable text, respond with 'NO TEXT'."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_base64
                                }
                            }
                        ]
                    }
                ]

                # Quick text extraction call
                quick_response = self.client.chat.completions.create(
                    model="OpenGVLab/InternVL3-78B-AWQ",
                    messages=quick_messages,
                    stream=False,
                    max_tokens=200,  # Shorter response for quick check
                    temperature=0.1
                )

                quick_text = quick_response.choices[0].message.content
                print(f"Quick text extraction for {image_path}: {quick_text[:100]}...")

                # Extract actual text content from quick response
                actual_text = self.extract_actual_text_content(quick_text)
                print(f"Actual text found: '{actual_text}'")

                # If no meaningful text found, skip detailed description
                if len(actual_text.strip()) < min_text_length or actual_text.strip().upper() == 'NO TEXT':
                    print(f"⚠️  Image {image_path} contains insufficient text ({len(actual_text)} characters), will not be inserted to vector database")
                    return "", False

                # Step 2: If meaningful text found, do detailed description
                print(f"📝 Meaningful text found, proceeding with detailed OCR for {image_path}")

                detailed_messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Please extract and describe all text content visible in this image. If there are charts, tables, or diagrams, describe their content and any visible text or numbers. Focus on extracting readable text accurately."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_base64
                                }
                            }
                        ]
                    }
                ]

                # Detailed OCR call
                response = self.client.chat.completions.create(
                    model="OpenGVLab/InternVL3-78B-AWQ",
                    messages=detailed_messages,
                    stream=False,
                    max_tokens=1000,
                    temperature=0.1
                )

                # Extract OCR text
                ocr_text = response.choices[0].message.content

                print(f"InternVL OCR result for {image_path}: {len(ocr_text)} characters")
                print(f"Original OCR Content: {ocr_text[:200]}...")

                # Clean and process OCR text to remove descriptive prefixes and improve search relevance
                cleaned_ocr_text = self.clean_ocr_text(ocr_text)

                print(f"Cleaned OCR Content: {cleaned_ocr_text[:200]}...")

                # Check if cleaned OCR text length meets requirements
                if cleaned_ocr_text.strip() and len(cleaned_ocr_text.strip()) >= min_text_length:
                    print(f"✅ Image {image_path} OCR successful, cleaned text length: {len(cleaned_ocr_text)}")
                    return cleaned_ocr_text.strip(), True
                else:
                    if not cleaned_ocr_text.strip():
                        print(f"⚠️  Image {image_path} OCR content filtered out as purely descriptive, will not be inserted to vector database")
                    else:
                        print(f"⚠️  Image {image_path} OCR text too short after cleaning ({len(cleaned_ocr_text)} < {min_text_length}), will not be inserted to vector database")
                    return "", False

            except Exception as e:
                print(f"InternVL OCR处理失败: {str(e)}")
                # 使用回退方案
                fallback_text = "this is image content for image"
                print(f"使用回退文本: {fallback_text}")
                return fallback_text, True

        except Exception as e:
            print(f"无法处理图像文件 {image_path}: {str(e)}")
            return "", False
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        使用MinerU处理PDF文件（支持虚拟环境回退）

        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录，如果为None则使用临时目录

        Returns:
            Tuple[layout_data, markdown_content]: 布局数据和Markdown内容
        """
        print(f"🔍 [PROCESSOR_DEBUG] 开始处理PDF: {pdf_path}")

        if not os.path.exists(pdf_path):
            self.logger.error(f"PDF文件不存在: {pdf_path}")
            return None, None

        # 首先尝试直接调用
        try:
            print(f"🔍 [PROCESSOR_DEBUG] 尝试直接MinerU调用...")
            return self._process_pdf_direct(pdf_path, output_dir)
        except (ImportError, NameError) as e:
            print(f"⚠️  [PROCESSOR_DEBUG] 直接调用失败: {str(e)}")
            print(f"🔄 [PROCESSOR_DEBUG] 回退到虚拟环境模式...")

            # 回退到虚拟环境模式
            try:
                from mineru_venv_integration import get_venv_processor
                venv_processor = get_venv_processor()
                if venv_processor.is_available():
                    print(f"✅ [PROCESSOR_DEBUG] 使用虚拟环境处理器")
                    return venv_processor.process_pdf(pdf_path, output_dir)
                else:
                    print(f"❌ [PROCESSOR_DEBUG] 虚拟环境处理器不可用")
                    return None, None
            except Exception as venv_e:
                print(f"❌ [PROCESSOR_DEBUG] 虚拟环境处理也失败: {str(venv_e)}")
                return None, None
        except Exception as e:
            print(f"❌ [PROCESSOR_DEBUG] 处理过程出错: {str(e)}")
            self.logger.error(f"MinerU处理失败: {str(e)}")
            return None, None

    def _process_pdf_direct(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """直接使用MinerU API处理PDF"""
        # 创建输出目录
        if output_dir is None:
            output_dir = tempfile.mkdtemp()
        else:
            os.makedirs(output_dir, exist_ok=True)

        # 配置MinerU参数
        if self.config_manager:
            parse_config = self.config_manager.get_parse_config()
            parse_config["output_dir"] = output_dir

            # 检查文件是否支持
            if not self.config_manager.is_file_supported(pdf_path):
                self.logger.warning(f"文件格式不支持: {pdf_path}")
                return None, None

            # 检查文件大小
            if not self.config_manager.check_file_size(pdf_path):
                self.logger.warning(f"文件大小超出限制: {pdf_path}")
                return None, None
        else:
            # 默认配置
            parse_config = {
                "parse_mode": "auto",  # 自动模式
                "output_format": ["json"],  # 输出格式
                "output_dir": output_dir,
                "lang": "auto",  # 自动语言检测
                "layout_config": {
                    "model": "doclayout_yolo"
                },
                "formula_config": {
                    "enable": True,
                    "mfd_model": "yolo_v8_mfd",
                    "mfr_model": "unimernet_small"
                },
                "table_config": {
                    "enable": True,
                    "model": "rapid_table"
                }
            }

        self.logger.info(f"开始使用MinerU处理PDF: {pdf_path}")

        # 调用MinerU API
        result = magic_pdf_parse(
            pdf_path=pdf_path,
            output_dir=output_dir,
            **parse_config
        )

        # 解析结果
        layout_data = None
        markdown_content = None

        if result and "layout" in result:
            layout_data = result["layout"]

        if result and "markdown" in result:
            markdown_content = result["markdown"]

        self.logger.info(f"MinerU处理完成: {pdf_path}")
        return layout_data, markdown_content
    
    def convert_to_chunks(self, layout_data: Dict, filename: str, output_dir: str = None) -> List[Tuple[str, Dict]]:
        """
        将MinerU的布局数据转换为与现有系统兼容的chunks格式

        Args:
            layout_data: MinerU输出的布局数据
            filename: 原始文件名
            output_dir: MinerU的输出目录，用于构建图片的完整路径

        Returns:
            List[Tuple[str, Dict]]: chunks列表，格式与chunk_pdf_advanced相同
        """
        chunks_with_metadata = []

        # 保存output_dir和文档名供图片处理使用
        self.current_output_dir = output_dir
        # 从filename中提取文档名（去掉扩展名）
        self.current_document_name = os.path.splitext(os.path.basename(filename))[0]

        try:
            if not layout_data or "pdf_info" not in layout_data:
                self.logger.warning("布局数据格式不正确")
                return chunks_with_metadata
            
            for page_idx, page_info in enumerate(layout_data["pdf_info"]):
                page_num = page_idx + 1
                
                if "preproc_blocks" not in page_info:
                    continue
                
                for block_idx, block in enumerate(page_info["preproc_blocks"]):
                    chunk_text = self._extract_text_from_block(block)
                    
                    if not chunk_text or chunk_text.strip() == "":
                        continue
                    
                    # 创建元数据
                    metadata = {
                        'start_page_num': page_num,
                        'end_page_num': page_num,
                        'start_line_num': block_idx + 1,
                        'end_line_num': block_idx + 1,
                        'content_type': block.get('type', 'text'),
                        'bbox': block.get('bbox', [0, 0, 0, 0]),
                        'source': filename,
                        'block_index': block_idx,
                        'confidence': block.get('score', 1.0)
                    }
                    
                    chunks_with_metadata.append((chunk_text, metadata))
            
            self.logger.info(f"转换完成，生成 {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata
            
        except Exception as e:
            self.logger.error(f"转换chunks失败: {str(e)}")
            return chunks_with_metadata
    
    def _extract_text_from_block(self, block: Dict) -> str:
        """从block中提取文本内容"""
        text_content = ""
        
        try:
            if block.get('type') == 'text':
                # 处理文本块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                
            elif block.get('type') == 'title':
                # 处理标题块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                                    
            elif block.get('type') == 'table':
                # 处理表格块
                if 'blocks' in block:
                    for sub_block in block['blocks']:
                        if 'lines' in sub_block:
                            for line in sub_block['lines']:
                                if 'spans' in line:
                                    for span in line['spans']:
                                        if span.get('type') == 'table' and 'html' in span:
                                            text_content += f"[TABLE: {span['html']}]"
                                        elif span.get('type') == 'text' and 'content' in span:
                                            text_content += span['content'] + " "
            
            elif block.get('type') == 'image':
                # 处理图像块 - 使用InternVL进行OCR处理
                # 首先尝试从block级别获取image_path
                image_path = block.get('image_path', '')

                # 如果block级别没有，尝试从lines/spans中查找
                if not image_path and 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'image' and 'image_path' in span:
                                    image_path = span['image_path']
                                    break
                        if image_path:
                            break

                # 构建完整的图片路径
                full_image_path = image_path
                if hasattr(self, 'current_output_dir') and self.current_output_dir and image_path:
                    # 如果image_path是相对路径，则与output_dir组合
                    if not os.path.isabs(image_path):
                        # 从当前处理的文档名推断目录结构
                        # 假设current_output_dir是uploads，需要找到文档名
                        if hasattr(self, 'current_document_name') and self.current_document_name:
                            doc_name = self.current_document_name
                        else:
                            # 如果没有设置文档名，尝试从其他地方推断
                            doc_name = getattr(self, 'document_name', 'unknown')

                        # MinerU的实际目录结构: uploads/document_name/document_name/auto/images/
                        possible_paths = [
                            # 标准MinerU结构: uploads/doc_name/doc_name/auto/images/
                            os.path.join(self.current_output_dir, doc_name, doc_name, 'auto', 'images', image_path),
                            # 备选结构: uploads/doc_name/auto/images/
                            os.path.join(self.current_output_dir, doc_name, 'auto', 'images', image_path),
                            # 直接在uploads下
                            os.path.join(self.current_output_dir, image_path),
                        ]

                        # 尝试找到存在的路径
                        for path in possible_paths:
                            if os.path.exists(path):
                                full_image_path = path
                                break

                print(f"🔍 [IMAGE_DEBUG] 原始路径: {image_path}")
                print(f"🔍 [IMAGE_DEBUG] 完整路径: {full_image_path}")
                print(f"🔍 [IMAGE_DEBUG] 文件存在: {os.path.exists(full_image_path) if full_image_path else False}")

                if full_image_path and os.path.exists(full_image_path):
                    print(f"🖼️  Processing image with InternVL: {full_image_path}")
                    ocr_text, should_include = self.process_image_with_internvl(full_image_path)
                    if should_include and ocr_text.strip():
                        text_content = ocr_text
                        print(f"✅ Image OCR successful: {len(text_content)} characters")
                    else:
                        text_content = f"[IMAGE: {os.path.basename(image_path)}]"
                        print(f"⚠️  Image OCR failed or no meaningful text, using placeholder")
                else:
                    text_content = f"[IMAGE: {os.path.basename(image_path) if image_path else 'unknown'}]"
                    print(f"⚠️  Image file not found: {full_image_path}")
                
            elif block.get('type') == 'formula':
                # 处理公式块
                if 'latex' in block:
                    text_content = f"[FORMULA: {block['latex']}]"
                    
        except Exception as e:
            self.logger.warning(f"提取文本失败: {str(e)}")
            
        return text_content.strip()

def test_mineru_integration():
    """测试MinerU集成功能"""
    if not MINERU_AVAILABLE:
        print("MinerU不可用，请先安装: pip install magic-pdf[full]")
        return
    
    processor = MinerUProcessor()
    
    # 测试PDF文件路径
    test_pdf = "uploads/test.pdf"  # 替换为实际的测试文件
    
    if os.path.exists(test_pdf):
        layout_data, markdown_content = processor.process_pdf(test_pdf)
        
        if layout_data:
            chunks = processor.convert_to_chunks(layout_data, "test.pdf")
            print(f"成功处理PDF，生成 {len(chunks)} 个文本块")
            
            # 显示前几个chunks
            for i, (text, metadata) in enumerate(chunks[:3]):
                print(f"\nChunk {i+1}:")
                print(f"Text: {text[:100]}...")
                print(f"Metadata: {metadata}")
        else:
            print("PDF处理失败")
    else:
        print(f"测试文件不存在: {test_pdf}")

if __name__ == "__main__":
    test_mineru_integration()
