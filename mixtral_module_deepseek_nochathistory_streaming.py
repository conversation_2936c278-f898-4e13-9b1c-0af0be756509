"""

    Module Name :           mixtral_module
    Last Modified Date :    3 Jan 2024

"""
import subprocess
from typing import Any

import chinese_converter
# Import Open-source Libraries
from langchain import HuggingFacePipeline, hub
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.chat_models import ChatOpenAI
from langchain_community.llms.llamafile import Llamafile
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LL<PERSON>hain
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_community.llms import Ollama
from langchain_community.chat_models import ChatOllama
from ollama import Client
from openai import OpenAI  # 添加OpenAI客户端导入
# import tools.gpu_detect
import langchain
# langchain.debug = True

# 使用与主chatbot相同的InternVL客户端配置
try:
    # 尝试从主模块导入已存在的客户端
    import __main__
    if hasattr(__main__, 'client'):
        client = __main__.client
        print("🔗 [CLIENT] Using OpenAI client from main module")
    else:
        # 如果主模块没有client，则创建新的
        client = OpenAI(api_key='ollama', base_url="http://*************:30290/v1")
        print("🔗 [CLIENT] Using model from server")
except:
    # 备用方案：直接创建客户端
    client = OpenAI(api_key='ollama', base_url="http://*************:30290/v1")
    print("🔗 [CLIENT] Using model from server")

# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# InternVL响应函数
def call_internvl(messages):
    """调用InternVL模型生成响应"""
    try:
        response = client.chat.completions.create(
            model="OpenGVLab/InternVL3-78B-AWQ",
            messages=messages,
            stream=False,
            max_tokens=2000,
            temperature=0.7
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"InternVL调用失败: {str(e)}")
        return "抱歉，生成响应时发生错误。"


# 新增：InternVL流式响应函数
def call_internvl_streaming(messages):
    """调用InternVL模型生成流式响应"""
    try:
        # 使用stream=True启用真实流式传输
        response = client.chat.completions.create(
            model="OpenGVLab/InternVL3-78B-AWQ",
            messages=messages,
            stream=True,  # 这里启用真实流式传输
            max_tokens=2000,
            temperature=0.7
        )
        
        # 逐个产出模型生成的片段
        for chunk in response:
            if chunk.choices and chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
                
    except Exception as e:
        print(f"InternVL流式调用失败: {str(e)}")
        yield "抱歉，生成响应时发生错误。"


# mixtral Module (现在使用InternVL)
def mixtral_response(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response using InternVL")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()
    
    # 使用InternVL替代DeepSeek R1
    # 原来的代码: llm = Ollama(base_url="http://*************:30233", model="deepseek-r1:32b")
    
    print("使用InternVL模型替代DeepSeek R1")
    
    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"InternVL Model准备时间 = {load_time}")

    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []
        
        print("处理不使用检索器的对话: ", len(chat_history))
        
        # 构建InternVL消息格式
        messages = []
        
        # 添加系统提示
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        else:
            messages.append({
                "role": "system",
                "content": "You are a helpful assistant. Answer the user's question based on the conversation history if provided."
            })
        
        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})
        
        # 添加当前问题
        messages.append({"role": "user", "content": question})
        
        # 生成AI响应
        airespone_starttime = datetime.now()
        
        ai_msg_content = call_internvl(messages)
        
        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    # With Vector Store (使用检索器)
    else:
        if chat_history is None:
            chat_history = []
        
        print(f"处理使用检索器的对话: {len(chat_history)} 条历史记录")
        
        # 获取相关文档
        context = ""
        try:
            if hasattr(retriever, 'get_relevant_documents'):
                docs = retriever.get_relevant_documents(question)
                context = "\n\n".join([doc.page_content for doc in docs])
                print(f"检索到 {len(docs)} 个相关文档，总长度: {len(context)} 字符")
            elif hasattr(retriever, 'invoke'):
                docs = retriever.invoke(question)
                if isinstance(docs, list):
                    context = "\n\n".join([doc.page_content if hasattr(doc, 'page_content') else str(doc) for doc in docs])
                else:
                    context = str(docs)
                print(f"通过invoke检索到文档，总长度: {len(context)} 字符")
            else:
                print("检索器没有可用的方法")
                context = ""
        except Exception as e:
            print(f"检索文档时出错: {str(e)}")
            context = ""
        
        # 构建消息
        messages = []
        
        # 添加系统提示和上下文
        if context:
            system_msg = """You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know.

Context: """ + context
        else:
            system_msg = system_prompt if system_prompt else "You are a helpful assistant."
        
        messages.append({"role": "system", "content": system_msg})
        
        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})
        
        # 添加当前问题
        messages.append({"role": "user", "content": question})

        # 生成AI响应
        airespone_starttime = datetime.now()
        
        ai_msg_content = call_internvl(messages)
        
        print("InternVL响应:", ai_msg_content[:200] + "..." if len(ai_msg_content) > 200 else ai_msg_content)
        
        _start = datetime.now()
        # ai_msg_content = chinese_converter.to_traditional(ai_msg_content)
        print("Translation Time: ", datetime.now() - _start)
        airesponse_endtime = datetime.now()

        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    print('>>> Generated AI Response using InternVL')

    return ai_msg_content


# 新增：支持流式传输的mixtral响应函数
def mixtral_response_streaming(
                    question,
                    system_prompt,
                    condense_system_prompt,
                    prompt_template,
                    model_para,
                    retriever,
                    chat_history=None):
    
    print("... Generating AI Response using InternVL with streaming")

    aimodel_starttime = datetime.now()
    print("使用InternVL模型替代DeepSeek R1（流式传输）")
    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"InternVL Model准备时间 = {load_time}")

    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []
        
        print("处理不使用检索器的对话（流式）: ", len(chat_history))
        
        # 构建InternVL消息格式
        messages = []
        
        # 添加系统提示
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        else:
            messages.append({
                "role": "system",
                "content": "You are a helpful assistant. Answer the user's question based on the conversation history if provided."
            })
        
        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})
        
        # 添加当前问题
        messages.append({"role": "user", "content": question})
        
        # 生成流式AI响应
        print("开始流式生成...")
        return call_internvl_streaming(messages)

    # With Vector Store (使用检索器)
    else:
        if chat_history is None:
            chat_history = []
        
        print(f"处理使用检索器的对话（流式）: {len(chat_history)} 条历史记录")
        
        # 获取相关文档
        context = ""
        try:
            if hasattr(retriever, 'get_relevant_documents'):
                docs = retriever.get_relevant_documents(question)
                context = "\n\n".join([doc.page_content for doc in docs])
                print(f"检索到 {len(docs)} 个相关文档，总长度: {len(context)} 字符")
            elif hasattr(retriever, 'invoke'):
                docs = retriever.invoke(question)
                if isinstance(docs, list):
                    context = "\n\n".join([doc.page_content if hasattr(doc, 'page_content') else str(doc) for doc in docs])
                else:
                    context = str(docs)
                print(f"通过invoke检索到文档，总长度: {len(context)} 字符")
            else:
                print("检索器没有可用的方法")
                context = ""
        except Exception as e:
            print(f"检索文档时出错: {str(e)}")
            context = ""
        
        # 构建消息
        messages = []
        
        # 添加系统提示和上下文
        if context:
            system_msg = """You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know.

Context: """ + context
        else:
            system_msg = system_prompt if system_prompt else "You are a helpful assistant."
        
        messages.append({"role": "system", "content": system_msg})
        
        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})
        
        # 添加当前问题
        messages.append({"role": "user", "content": question})

        # 生成流式AI响应
        print("开始流式生成（带检索）...")
        return call_internvl_streaming(messages)
