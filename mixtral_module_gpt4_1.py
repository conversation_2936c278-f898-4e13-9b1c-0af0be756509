"""

    Module Name :           mixtral_module (Azure GPT-4.1 Integration)
    Last Modified Date :    25 Jun 2025

    This module now uses Azure OpenAI's GPT-4.1 model instead of InternVL/Mixtral.
    It supports both streaming and non-streaming responses.

"""
import subprocess
from typing import Any

import chinese_converter
# Import Open-source Libraries
from langchain import HuggingFacePipeline, hub
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import StrOutputParser
from langchain_community.chat_models import ChatOpenAI
from langchain_community.llms.llamafile import Llamafile
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.messages import HumanMessage
import transformers
from transformers import AutoTokenizer
import torch
from huggingface_hub import hf_hub_download
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.llms import LlamaCpp
from langchain import PromptTemplate, LLMChain
from datetime import datetime

# Import Self-defined Modules
import preprocessing
from langchain_community.llms import Ollama
from langchain_community.chat_models import ChatOllama
from ollama import Client
from openai import OpenAI  # 添加OpenAI客户端导入

# 添加Azure OpenAI支持
try:
    from openai import AzureOpenAI
except ImportError:
    print("Azure OpenAI module not found, installing...")
    import subprocess

    subprocess.check_call(["pip", "install", "openai>=1.0.0"])
    from openai import AzureOpenAI
# import tools.gpu_detect
import langchain
import os

# langchain.debug = True

# Azure OpenAI API Configuration
AZURE_OPENAI_API_KEY = 'DJ2Gpu5cCF3hNC45S46RXXASl8taegMDWHnkF5GjSMhNQTrkUOzbJQQJ99BEACfhMk5XJ3w3AAABACOGbdte'
ENDPOINT_URL = 'https://nerllm.openai.azure.com'
DEPLOYMENT_NAME = 'gpt-4.1'
API_VERSION = '2025-01-01-preview'

# Build Azure OpenAI API URL
API_URL = f"{ENDPOINT_URL}/openai/deployments/{DEPLOYMENT_NAME}/chat/completions?api-version={API_VERSION}"

# GPT-4o Azure OpenAI API 配置
AZURE_OPENAI_API_KEY_4O = os.getenv('AZURE_OPENAI_API_KEY_4O',
                                    '1ZVPYTWJTBRce59D4W6hYCeTiIsBfwSgQzaqFCQqtUV3ldrCZRIvJQQJ99BEACYeBjFXJ3w3AAABACOG7uxm')
ENDPOINT_URL_4O = os.getenv('ENDPOINT_URL_4O', 'https://nerllm-4o.openai.azure.com/')
DEPLOYMENT_NAME_4O = os.getenv('DEPLOYMENT_NAME_4O', 'gpt-4o')
API_VERSION_4O = os.getenv('API_VERSION_4O', '2025-01-01-preview')

# 配置API密钥
API_KEY = os.getenv("AZURE_OPENAI_API_KEY", AZURE_OPENAI_API_KEY)
USE_MOCK_API = os.getenv("USE_MOCK_API", "false").lower() == "true"

# Initialize Azure OpenAI client
try:
    # 尝试从主模块导入已存在的客户端
    import __main__

    if hasattr(__main__, 'client'):
        client = __main__.client
        print("🔗 [CLIENT] Using Azure OpenAI client from main module")
    else:
        # 初始化Azure OpenAI客户端
        client = AzureOpenAI(
            api_key=API_KEY,
            api_version=API_VERSION,
            azure_endpoint=ENDPOINT_URL
        )
        print("Azure OpenAI client (GPT-4.1) initialized successfully")
except Exception as e:
    print(f"Error initializing Azure OpenAI client: {e}")
    # 备用方案：尝试使用本地Ollama客户端
    client = OpenAI(api_key='ollama', base_url="http://*************:30290/v1")
    print("⚠️ Falling back to Ollama client")


# init mixtral pipeline
def init_mixtral_piepeline(model_para):
    pipeline = transformers.pipeline(
        model_para["task"],
        model=model_para["model"],
        device_map=model_para["device_map"],
        model_kwargs={"torch_dtype": eval(model_para["torch_dtype"])},
        max_new_tokens=model_para["max_new_tokens"],
        repetition_penalty=model_para["repetition_penalty"],
        return_full_text=eval(model_para["return_full_text"]),
        pad_token_id=2,
        eos_token_id=2
    )
    return pipeline


# GPT-4.1响应函数
def call_internvl(messages, custom_client=None, model_name=None, stream=False):
    """调用Azure GPT-4.1模型生成响应

    Args:
        messages: 消息列表
        custom_client: 可选的自定义客户端
        model_name: 可选的模型名称/部署名称
        stream: 是否使用流式输出
    """
    try:
        # 确定使用哪个客户端和模型名称
        used_client = custom_client if custom_client else client
        used_model = model_name if model_name else DEPLOYMENT_NAME

        if stream:
            # 使用流式输出
            streaming_callback = AzureOpenAIStreamingCallback()

            # 使用Azure OpenAI客户端创建聊天完成（流式）
            response = used_client.chat.completions.create(
                model=used_model,  # 使用部署名称而不是模型名称
                messages=messages,
                max_tokens=5000,
                temperature=0.7,
                stream=True
            )

            # 处理流式输出
            for chunk in response:
                streaming_callback(chunk)

            return streaming_callback.get_response_text()
        else:
            # 非流式输出
            response = used_client.chat.completions.create(
                model=used_model,  # 使用部署名称而不是模型名称
                messages=messages,
                max_tokens=2000,
                temperature=0.7,
                stream=False
            )
            return response.choices[0].message.content
    except Exception as e:
        print(f"GPT-4.1调用失败: {str(e)}")
        # 在错误信息中包含更多详细信息
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return "抱歉，生成响应时发生错误。请检查日志获取详细信息。"


# 创建一个流式输出回调处理器
streaming_callback = StreamingStdOutCallbackHandler()


# 自定义Azure OpenAI流式输出回调
class AzureOpenAIStreamingCallback:
    def __init__(self):
        self.collected_chunks = []
        self.collected_messages = []

    def __call__(self, chunk):
        if chunk.choices:
            content = chunk.choices[0].delta.content
            if content is not None:
                print(content, end="", flush=True)
                self.collected_chunks.append(content)
                self.collected_messages.append(content)

    def get_response_text(self):
        return "".join(self.collected_messages)


# mixtral Module (现在使用Azure GPT-4.1)
def mixtral_response(
        question,
        system_prompt,
        condense_system_prompt,
        prompt_template,
        model_para,
        retriever,
        chat_history=None):
    print("... Generating AI Response using Azure GPT-4.1")

    ai_msg_content = ''

    aimodel_starttime = datetime.now()

    # 检查model_para中是否提供了自定义API配置
    global client
    custom_client = None

    # 从模型参数获取 API 配置，如果没有则使用默认值
    api_key = model_para.get("api_key", AZURE_OPENAI_API_KEY)
    api_base = model_para.get("api_base", ENDPOINT_URL)
    model_name = model_para.get("model", DEPLOYMENT_NAME)
    api_version = model_para.get("api_version", API_VERSION)

    print(f"DEBUG: 使用Azure OpenAI - API Base: {api_base}")
    print(f"DEBUG: 使用模型: {model_name}")

    try:
        # 如果提供了自定义配置，创建新的客户端
        if api_key != AZURE_OPENAI_API_KEY or api_base != ENDPOINT_URL:
            custom_client = AzureOpenAI(
                api_key=api_key,
                api_version=api_version,
                azure_endpoint=api_base
            )
            print("创建了新的Azure OpenAI客户端实例")
    except Exception as e:
        print(f"创建自定义Azure OpenAI客户端时出错: {e}")
        # 继续使用全局客户端

    # 使用Azure GPT-4.1替代之前的模型
    print("使用Azure GPT-4.1模型替代之前的模型")

    aimodel_endtime = datetime.now()
    load_time = aimodel_endtime - aimodel_starttime
    print(f"Azure GPT-4.1 Model准备时间 = {load_time}")

    if not retriever:
        # Initialize chat_history as an empty list if it's None
        if chat_history is None:
            chat_history = []

        print("处理不使用检索器的对话: ", len(chat_history))

        # 构建GPT-4.1消息格式
        messages = []

        # 添加系统提示
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        else:
            messages.append({
                "role": "system",
                "content": "You are a helpful assistant. Answer the user's question based on the conversation history if provided."
            })

        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})

        # 添加当前问题
        messages.append({"role": "user", "content": question})

        # 生成AI响应
        airespone_starttime = datetime.now()

        # 检查是否需要流式输出
        stream_mode = model_para.get("stream", False)
        ai_msg_content = call_internvl(messages, custom_client, model_name, stream=stream_mode)

        airesponse_endtime = datetime.now()
        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    # With Vector Store (使用检索器)
    else:
        if chat_history is None:
            chat_history = []

        print(f"处理使用检索器的对话: {len(chat_history)} 条历史记录")

        # 获取相关文档
        context = ""
        try:
            if hasattr(retriever, 'get_relevant_documents'):
                docs = retriever.get_relevant_documents(question)
                context = "\n\n".join([doc.page_content for doc in docs])
                print(f"检索到 {len(docs)} 个相关文档，总长度: {len(context)} 字符")
            elif hasattr(retriever, 'invoke'):
                docs = retriever.invoke(question)
                if isinstance(docs, list):
                    context = "\n\n".join(
                        [doc.page_content if hasattr(doc, 'page_content') else str(doc) for doc in docs])
                else:
                    context = str(docs)
                print(f"通过invoke检索到文档，总长度: {len(context)} 字符")
            else:
                print("检索器没有可用的方法")
                context = ""
        except Exception as e:
            print(f"检索文档时出错: {str(e)}")
            context = ""

        # 构建消息
        messages = []

        # 添加系统提示和上下文
        if context:
            system_msg = """You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know.

Context: """ + context
        else:
            system_msg = system_prompt if system_prompt else "You are a helpful assistant."

        messages.append({"role": "system", "content": system_msg})

        # 添加聊天历史
        for msg in chat_history:
            if hasattr(msg, 'type') and hasattr(msg, 'content'):
                if msg.type == 'human':
                    messages.append({"role": "user", "content": msg.content})
                elif msg.type == 'ai':
                    messages.append({"role": "assistant", "content": msg.content})

        # 添加当前问题
        messages.append({"role": "user", "content": question})

        # 生成AI响应
        airespone_starttime = datetime.now()

        # 检查是否需要流式输出
        stream_mode = model_para.get("stream", False)
        ai_msg_content = call_internvl(messages, custom_client, model_name, stream=stream_mode)

        print("GPT-4.1响应:", ai_msg_content[:200] + "..." if len(ai_msg_content) > 200 else ai_msg_content)

        _start = datetime.now()
        # ai_msg_content = chinese_converter.to_traditional(ai_msg_content)
        print("Translation Time: ", datetime.now() - _start)
        airesponse_endtime = datetime.now()

        print(f"AI Response Time = {airesponse_endtime - airespone_starttime}")

    print('>>> Generated AI Response using Azure GPT-4.1')

    return ai_msg_content
