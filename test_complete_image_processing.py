#!/usr/bin/env python3
"""
测试完整的图片处理流程
"""

import os
import sys
import json

# 添加当前目录到Python路径
sys.path.append('.')

from mineru_integration import MinerUProcessor

def test_complete_processing():
    """测试完整的图片处理流程"""
    print("🔍 测试完整的MinerU图片处理流程")
    
    # 使用已经存在的layout数据
    layout_file = "uploads/test2/test2/auto/test2_middle.json"
    
    if not os.path.exists(layout_file):
        print(f"❌ 布局文件不存在: {layout_file}")
        return False
    
    print(f"📄 读取布局文件: {layout_file}")
    
    try:
        with open(layout_file, 'r', encoding='utf-8') as f:
            layout_data = json.load(f)
        
        print(f"✅ 布局文件读取成功")
        
        # 创建处理器
        processor = MinerUProcessor()
        
        # 设置输出目录（模拟实际调用时的设置）
        output_dir = "uploads/test2"
        
        # 转换为chunks
        print(f"🔄 开始转换为chunks...")
        chunks_with_metadata = processor.convert_to_chunks(layout_data, "test2.pdf", output_dir)
        
        if chunks_with_metadata:
            print(f"✅ 成功生成 {len(chunks_with_metadata)} 个文本块")
            
            # 统计不同类型的内容
            content_types = {}
            image_chunks = []
            
            for chunk_text, metadata in chunks_with_metadata:
                content_type = metadata.get('content_type', 'text')
                content_types[content_type] = content_types.get(content_type, 0) + 1
                
                if content_type == 'image':
                    image_chunks.append((chunk_text, metadata))
            
            print(f"📊 内容类型统计: {content_types}")
            
            # 显示前几个图片块的处理结果
            print(f"\n🖼️  图片处理结果 (前5个):")
            for i, (text, metadata) in enumerate(image_chunks[:5]):
                print(f"  图片 {i+1}:")
                print(f"    页码: {metadata.get('start_page_num', 'N/A')}")
                print(f"    置信度: {metadata.get('confidence', 'N/A')}")
                print(f"    文本长度: {len(text)} 字符")
                print(f"    文本预览: {text[:100]}...")
                print()
            
            # 检查是否有成功的OCR结果
            successful_ocr = 0
            for text, metadata in image_chunks:
                if not text.startswith('[IMAGE:') and len(text.strip()) > 10:
                    successful_ocr += 1
            
            print(f"📈 OCR成功率: {successful_ocr}/{len(image_chunks)} ({successful_ocr/len(image_chunks)*100:.1f}%)")
            
            return True
        else:
            print("❌ 转换失败，没有生成chunks")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_processing()
    if success:
        print("\n🎉 完整图片处理测试成功！")
    else:
        print("\n💥 完整图片处理测试失败！")
