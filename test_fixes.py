#!/usr/bin/env python3
"""
测试脚本：验证text_with_title合并和图片内容修复
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_get_element_description():
    """测试图片描述处理函数"""
    print("=== 测试图片描述处理函数 ===")
    
    # 导入函数
    from chatbot_newui_new_version_v7_1 import get_element_description
    
    # 测试用例
    test_cases = [
        ("image", {}, None),  # None描述
        ("image", {}, ""),    # 空描述
        ("image", {}, "   "), # 空白描述
        ("image", {}, "这是一个包含文字的图片"), # 正常描述
    ]
    
    for i, (element_type, element, description) in enumerate(test_cases):
        result = get_element_description(element_type, element, description)
        print(f"测试 {i+1}: 输入='{description}' -> 输出='{result}'")
    
    print()

def test_merge_short_text_chunks():
    """测试文本块合并功能"""
    print("=== 测试文本块合并功能 ===")
    
    # 导入函数
    from chatbot_newui_new_version_v7_1 import merge_short_text_chunks
    
    # 创建测试数据
    test_chunks = [
        ("價單", {"content_type": "title", "start_page_num": 1, "end_page_num": 1}),
        ("Price List", {"content_type": "title", "start_page_num": 1, "end_page_num": 1}),
        ("這是一個很長的文本段落，包含了很多內容，用來測試合併功能是否正常工作。", 
         {"content_type": "text", "start_page_num": 1, "end_page_num": 1}),
        ("短文本", {"content_type": "text", "start_page_num": 1, "end_page_num": 1}),
        ("表格內容", {"content_type": "table", "start_page_num": 1, "end_page_num": 1}),
    ]
    
    print(f"原始文本块数量: {len(test_chunks)}")
    for i, (text, metadata) in enumerate(test_chunks):
        print(f"  {i+1}. [{metadata['content_type']}] {text[:30]}...")
    
    # 执行合并
    merged_chunks = merge_short_text_chunks(test_chunks)
    
    print(f"\n合并后文本块数量: {len(merged_chunks)}")
    for i, (text, metadata) in enumerate(merged_chunks):
        content_type = metadata.get('content_type', 'unknown')
        print(f"  {i+1}. [{content_type}] {text[:50]}...")
        if content_type == 'text_with_title':
            print(f"      ✅ 成功生成 text_with_title!")
    
    print()

def main():
    """主测试函数"""
    print("开始测试修复...")
    
    try:
        test_get_element_description()
        test_merge_short_text_chunks()
        print("✅ 所有测试完成!")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
