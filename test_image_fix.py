#!/usr/bin/env python3
"""
测试图片路径修复的脚本
"""

import os
import json
from mineru_integration import MinerUProcessor

def test_image_processing():
    """测试图片处理功能"""
    
    # 创建处理器实例
    processor = MinerUProcessor()
    
    # 设置当前输出目录和文档名
    processor.current_output_dir = 'uploads'
    processor.current_document_name = 'test3'  # 测试用的文档名
    
    print(f"🔍 测试设置:")
    print(f"   current_output_dir: {processor.current_output_dir}")
    print(f"   current_document_name: {processor.current_document_name}")
    
    # 测试路径构建
    test_image_path = "some_image.jpg"  # 假设的图片文件名
    
    # 构建可能的路径
    possible_paths = [
        # 标准MinerU结构: uploads/doc_name/doc_name/auto/images/
        os.path.join(processor.current_output_dir, processor.current_document_name, processor.current_document_name, 'auto', 'images', test_image_path),
        # 备选结构: uploads/doc_name/auto/images/
        os.path.join(processor.current_output_dir, processor.current_document_name, 'auto', 'images', test_image_path),
        # 直接在uploads下
        os.path.join(processor.current_output_dir, test_image_path),
    ]
    
    print(f"\n🔍 测试路径构建:")
    for i, path in enumerate(possible_paths, 1):
        exists = os.path.exists(path)
        print(f"   路径 {i}: {path}")
        print(f"   存在: {exists}")
        if exists:
            print(f"   ✅ 找到有效路径!")
            break
    
    # 检查实际的test3目录结构
    test3_base = os.path.join('uploads', 'test3')
    if os.path.exists(test3_base):
        print(f"\n🔍 test3目录结构:")
        for root, dirs, files in os.walk(test3_base):
            level = root.replace(test3_base, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files[:5]:  # 只显示前5个文件
                print(f"{subindent}{file}")
            if len(files) > 5:
                print(f"{subindent}... 还有 {len(files) - 5} 个文件")
    
    # 如果有JSON文件，测试实际的图片块处理
    json_file = 'uploads/test3_mineru.json'
    if os.path.exists(json_file):
        print(f"\n🔍 测试实际JSON处理:")
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 查找第一个图片块
        image_block_found = False
        image_path = ""
        for page in data.get('pdf_info', []):
            for block in page.get('preproc_blocks', []):
                if block.get('type') == 'image':
                    print(f"   找到图片块: {block.get('type')}")

                    # 测试图片路径提取
                    image_path = block.get('image_path', '')
                    print(f"   block级别路径: '{image_path}'")

                    if not image_path and 'lines' in block:
                        print(f"   检查lines数量: {len(block['lines'])}")
                        for i, line in enumerate(block['lines']):
                            if 'spans' in line:
                                print(f"   line {i} spans数量: {len(line['spans'])}")
                                for j, span in enumerate(line['spans']):
                                    print(f"     span {j}: type='{span.get('type')}', has_image_path={('image_path' in span)}")
                                    if span.get('type') == 'image' and 'image_path' in span:
                                        image_path = span['image_path']
                                        print(f"   ✅ 从spans提取的路径: '{image_path}'")
                                        break
                            if image_path:
                                break

                    print(f"   最终提取的图片路径: '{image_path}'")
                    
                    if image_path:
                        # 测试完整路径构建
                        possible_paths = [
                            os.path.join(processor.current_output_dir, processor.current_document_name, processor.current_document_name, 'auto', 'images', image_path),
                            os.path.join(processor.current_output_dir, processor.current_document_name, 'auto', 'images', image_path),
                            os.path.join(processor.current_output_dir, image_path),
                        ]
                        
                        for i, path in enumerate(possible_paths, 1):
                            exists = os.path.exists(path)
                            print(f"   测试路径 {i}: {path}")
                            print(f"   存在: {exists}")
                            if exists:
                                print(f"   ✅ 找到图片文件!")
                                break
                    
                    image_block_found = True
                    break
            if image_block_found:
                break
        
        if not image_block_found:
            print("   ⚠️  未找到图片块")
    else:
        print(f"\n⚠️  JSON文件不存在: {json_file}")

if __name__ == "__main__":
    test_image_processing()
