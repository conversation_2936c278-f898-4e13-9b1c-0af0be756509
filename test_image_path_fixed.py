#!/usr/bin/env python3
"""
测试修复后的MinerU图片路径解析逻辑
"""

import os
import sys

def test_path_construction():
    """测试路径构建逻辑"""
    print("🔍 测试MinerU图片路径构建逻辑")
    
    # 模拟参数
    current_output_dir = "uploads/test2"
    image_path = "455f847c69a379a1e29aedbdfe71241dd9cf642aa843d00b6a7ef6ae836c18d3.jpg"
    
    # 应用修复后的逻辑
    doc_name = os.path.basename(current_output_dir)
    mineru_images_dir = os.path.join(current_output_dir, doc_name, 'auto', 'images')
    full_image_path = os.path.join(mineru_images_dir, image_path)
    
    print(f"📁 current_output_dir: {current_output_dir}")
    print(f"📄 image_path: {image_path}")
    print(f"📂 doc_name: {doc_name}")
    print(f"📂 mineru_images_dir: {mineru_images_dir}")
    print(f"🖼️  full_image_path: {full_image_path}")
    print(f"✅ 文件存在: {os.path.exists(full_image_path)}")
    
    # 测试几个已知存在的图片文件
    test_images = [
        "455f847c69a379a1e29aedbdfe71241dd9cf642aa843d00b6a7ef6ae836c18d3.jpg",
        "0b5a4ff362dbda05d9a207612d28707f4302609fc6a2e3b0dca7f3a399d15893.jpg",
        "cdc67eace75e49e59c34b4b5534cb3f0c5a46536a6c3cca5bb663d3ee4ae6ee7.jpg"
    ]
    
    print("\n🧪 测试多个图片文件:")
    for img in test_images:
        full_path = os.path.join(mineru_images_dir, img)
        exists = os.path.exists(full_path)
        print(f"  {'✅' if exists else '❌'} {img}: {exists}")
    
    return True

if __name__ == "__main__":
    test_path_construction()
