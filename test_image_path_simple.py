#!/usr/bin/env python3
"""
简化的图片路径测试
"""

import os
import json

def test_image_path_resolution():
    """测试图片路径解析逻辑"""
    print("=== 测试图片路径解析逻辑 ===")
    
    # 模拟MinerU的输出目录
    document_name = "thearlessalesbrochurefull20250425"
    output_dir = f"uploads/{document_name}"
    
    # 模拟从JSON文件中读取的图片路径
    sample_image_paths = [
        "996223c5912b63d1feccfec0bf2bd64ca156346ac1e0a7a2e211c9bf0d720398.jpg",
        "4199f402c5bd63fb379a806b7cafc40d21a2efe4d5a02c256e342cc2c5a9fd3c.jpg",
        "1010875c255eee9cd3417742a556b4e48ece4ca65f1f2ca68058cf3aed8f5dfd.jpg"
    ]
    
    print(f"输出目录: {output_dir}")
    print(f"输出目录存在: {os.path.exists(output_dir)}")
    
    for image_path in sample_image_paths:
        # 模拟修复后的路径解析逻辑
        if not os.path.isabs(image_path):
            full_image_path = os.path.join(output_dir, image_path)
        else:
            full_image_path = image_path
            
        print(f"原始路径: {image_path}")
        print(f"完整路径: {full_image_path}")
        print(f"文件存在: {os.path.exists(full_image_path)}")
        print("---")

def test_json_structure():
    """测试JSON文件结构"""
    print("\n=== 测试JSON文件结构 ===")
    
    json_file = "uploads/thearlessalesbrochurefull20250425.json"
    if os.path.exists(json_file):
        print(f"JSON文件存在: {json_file}")
        
        # 读取JSON文件的一小部分来检查结构
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                print(f"JSON文件加载成功")
                
                # 检查结构
                if 'pdf_info' in data:
                    pdf_info = data['pdf_info']
                    print(f"pdf_info 数组长度: {len(pdf_info)}")
                    
                    if len(pdf_info) > 0 and 'preproc_blocks' in pdf_info[0]:
                        blocks = pdf_info[0]['preproc_blocks']
                        print(f"第一页 preproc_blocks 数量: {len(blocks)}")
                        
                        # 查找包含image_path的块
                        image_blocks = 0
                        for block in blocks[:10]:  # 只检查前10个块
                            if 'image_path' in block:
                                image_blocks += 1
                                print(f"发现图片块: {block['image_path']}")
                        
                        print(f"前10个块中包含图片的数量: {image_blocks}")
                
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
    else:
        print(f"JSON文件不存在: {json_file}")

def main():
    """主测试函数"""
    print("开始简化的图片路径测试...")
    
    test_image_path_resolution()
    test_json_structure()
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
