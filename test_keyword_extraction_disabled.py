#!/usr/bin/env python3
"""
测试关键词提取功能是否已被成功注释掉
"""

import sys
import os

# 添加kg_construction目录到Python路径
sys.path.append('kg_construction')

def test_keyword_extraction_disabled():
    """测试关键词提取功能是否已被禁用"""
    print("🔍 测试关键词提取功能是否已被禁用")
    
    try:
        # 导入相关模块
        from kg_construction.main import insert_chunks
        from kg_construction.CustomNodes import ChunkNode, ImageNode
        
        print("✅ 成功导入kg_construction模块")
        
        # 创建测试节点
        test_nodes = [
            ChunkNode(
                id="test_chunk_1",
                text="This is a test document about insurance policies and health coverage.",
                parent="test_document.pdf",
                start_page_num=1,
                end_page_num=1
            ),
            ChunkNode(
                id="test_chunk_2", 
                text="The policy covers critical illness and provides comprehensive benefits.",
                parent="test_document.pdf",
                start_page_num=1,
                end_page_num=1
            )
        ]
        
        print(f"📄 创建了 {len(test_nodes)} 个测试节点")
        
        # 检查main.py中的代码修改
        main_file_path = "kg_construction/main.py"
        with open(main_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "# entities = RelationshipFinder.find_all_entities(nodes)" in content:
            print("✅ 关键词提取功能已被成功注释掉")
        else:
            print("❌ 关键词提取功能注释失败")
            return False
            
        if "entities = []  # 设置为空列表，跳过关键词提取" in content:
            print("✅ entities变量已被设置为空列表")
        else:
            print("❌ entities变量设置失败")
            return False
        
        print("🎉 关键词提取功能已成功禁用！")
        print("📝 修改内容:")
        print("   - RelationshipFinder.find_all_entities(nodes) 已被注释")
        print("   - entities 变量被设置为空列表 []")
        print("   - 这将跳过所有关键词提取处理，提高处理速度")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_keyword_extraction_disabled()
    if success:
        print("\n🎯 关键词提取功能已成功禁用，系统处理速度将得到提升！")
    else:
        print("\n💥 测试失败，请检查修改是否正确！")
