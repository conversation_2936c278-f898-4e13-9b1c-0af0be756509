#!/usr/bin/env python3
"""
测试MinerU图片路径修复
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_path_handling():
    """测试图片路径处理逻辑"""
    print("=== 测试图片路径处理逻辑 ===")
    
    try:
        from mineru_integration import MinerUProcessor
        
        # 创建测试处理器
        processor = MinerUProcessor()
        
        # 模拟MinerU布局数据
        test_layout_data = {
            "pdf_info": [
                {
                    "preproc_blocks": [
                        {
                            "type": "image",
                            "image_path": "images/test_image.png",  # 相对路径
                            "bbox": [100, 100, 200, 200]
                        },
                        {
                            "type": "text", 
                            "lines": [
                                {
                                    "spans": [
                                        {
                                            "type": "text",
                                            "content": "这是测试文本"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        # 创建临时输出目录和测试图片
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建images子目录
            images_dir = os.path.join(temp_dir, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            # 创建测试图片文件
            test_image_path = os.path.join(images_dir, "test_image.png")
            with open(test_image_path, 'wb') as f:
                # 创建一个简单的PNG文件头（最小可用PNG）
                png_header = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
                f.write(png_header)
            
            print(f"创建测试图片: {test_image_path}")
            print(f"图片文件存在: {os.path.exists(test_image_path)}")
            
            # 测试转换
            chunks = processor.convert_to_chunks(test_layout_data, "test.pdf", temp_dir)
            
            print(f"生成chunks数量: {len(chunks)}")
            
            for i, (text, metadata) in enumerate(chunks):
                print(f"Chunk {i+1}:")
                print(f"  类型: {metadata.get('content_type', 'unknown')}")
                print(f"  文本: {text[:100]}...")
                
                if metadata.get('content_type') == 'image':
                    if "[IMAGE:" in text and text != "[IMAGE: ]":
                        print("  ✅ 图片处理成功，包含内容")
                    else:
                        print("  ❌ 图片处理失败，内容为空")
        
        print("✅ 图片路径处理测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_uploads_folder_structure():
    """测试uploads文件夹结构"""
    print("\n=== 测试uploads文件夹结构 ===")
    
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        print(f"📁 uploads目录存在: {uploads_dir}")
        
        # 列出uploads目录下的内容
        for item in os.listdir(uploads_dir):
            item_path = os.path.join(uploads_dir, item)
            if os.path.isdir(item_path):
                print(f"  📁 子目录: {item}")
                # 检查是否有图片文件
                for subitem in os.listdir(item_path):
                    if subitem.lower().endswith(('.png', '.jpg', '.jpeg')):
                        print(f"    🖼️  图片: {subitem}")
            elif item.lower().endswith('.pdf'):
                print(f"  📄 PDF文件: {item}")
    else:
        print(f"❌ uploads目录不存在: {uploads_dir}")

def main():
    """主测试函数"""
    print("开始测试MinerU图片路径修复...")
    
    test_image_path_handling()
    test_uploads_folder_structure()
    
    print("\n✅ 所有测试完成!")

if __name__ == "__main__":
    main()
