#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MinerU图像处理功能
验证v5的InternVL图像处理功能是否成功集成到v7的MinerU处理流程中
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

try:
    from mineru_integration import MinerUProcessor
    print("✅ 成功导入MinerUProcessor")
except ImportError as e:
    print(f"❌ 导入MinerUProcessor失败: {str(e)}")
    sys.exit(1)

def test_image_processing_functions():
    """测试图像处理相关函数"""
    print("\n🧪 测试图像处理函数...")
    
    try:
        processor = MinerUProcessor()
        print("✅ MinerUProcessor初始化成功")
        
        # 测试base64转换函数
        test_image_path = "test_image.png"
        if os.path.exists(test_image_path):
            try:
                base64_uri = processor.png_image_to_base64_data_uri(test_image_path)
                print(f"✅ Base64转换成功: {len(base64_uri)} 字符")
            except Exception as e:
                print(f"❌ Base64转换失败: {str(e)}")
        else:
            print("⚠️  测试图像文件不存在，跳过base64转换测试")
        
        # 测试OCR文本清理函数
        test_ocr_texts = [
            "The image contains the following text: - AMBER PLACE - 恒珀 - SALES BROCHURE 售樓說明書",
            "Certainly! Here is the extracted text: Price List 价格表",
            "NO TEXT",
            "The image shows a patterned design with no text content."
        ]
        
        print("\n📝 测试OCR文本清理...")
        for i, test_text in enumerate(test_ocr_texts):
            print(f"\n测试 {i+1}:")
            print(f"  原始: {test_text}")
            
            # 测试实际文本提取
            actual_text = processor.extract_actual_text_content(test_text)
            print(f"  提取: '{actual_text}'")
            
            # 测试文本清理
            cleaned_text = processor.clean_ocr_text(test_text)
            print(f"  清理: '{cleaned_text}'")
        
        print("\n✅ 图像处理函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 图像处理函数测试失败: {str(e)}")
        return False

def test_mineru_integration():
    """测试MinerU集成功能"""
    print("\n🧪 测试MinerU集成...")
    
    # 查找测试PDF文件
    test_pdfs = []
    for ext in ['*.pdf']:
        test_pdfs.extend(Path('.').glob(ext))
    
    if not test_pdfs:
        print("⚠️  未找到测试PDF文件，跳过MinerU集成测试")
        return True
    
    test_pdf = str(test_pdfs[0])
    print(f"📄 使用测试文件: {test_pdf}")
    
    try:
        processor = MinerUProcessor()
        
        # 处理PDF
        layout_data, markdown_content = processor.process_pdf(test_pdf)
        
        if layout_data:
            print(f"✅ PDF处理成功")
            
            # 转换为chunks
            chunks = processor.convert_to_chunks(layout_data, os.path.basename(test_pdf))
            print(f"✅ 生成 {len(chunks)} 个文本块")
            
            # 检查是否有图像块被处理
            image_chunks = 0
            for text, metadata in chunks:
                if metadata.get('content_type') == 'image':
                    image_chunks += 1
                    print(f"🖼️  发现图像块: {text[:100]}...")
            
            print(f"📊 图像块数量: {image_chunks}")
            
            if image_chunks > 0:
                print("✅ 图像处理集成测试成功")
            else:
                print("⚠️  未发现图像块，可能PDF中没有图像或图像处理被跳过")
            
            return True
        else:
            print("❌ PDF处理失败")
            return False
            
    except Exception as e:
        print(f"❌ MinerU集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试MinerU图像处理功能集成...")
    
    # 测试1: 图像处理函数
    test1_passed = test_image_processing_functions()
    
    # 测试2: MinerU集成
    test2_passed = test_mineru_integration()
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"  图像处理函数: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  MinerU集成: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！v5的InternVL图像处理功能已成功集成到v7的MinerU处理流程中。")
        print("\n📝 功能说明:")
        print("  - MinerU提取的图像现在会使用InternVL进行OCR文字识别")
        print("  - 图像OCR结果会经过AI清理，去除描述性文字")
        print("  - 只有包含有意义文字的图像才会被插入向量数据库")
        print("  - 无文字或文字过少的图像会使用简单占位符")
    else:
        print("\n❌ 部分测试失败，请检查错误信息并修复问题。")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
